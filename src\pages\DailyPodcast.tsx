import React, { useState, useMemo, useRef, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  Play,
  Pause,
  Volume2,
  Calendar,
  Filter,
  Loader2,
  AlertCircle,
  FileAudio,
  Clock,
  Settings,
  Ski<PERSON><PERSON><PERSON>,
  Ski<PERSON>Forward
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import Navbar from '@/components/Navbar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { DatePicker } from '@/components/ui/date-picker';
import { usePodcasts, PodcastTask } from '@/hooks/usePodcasts';
import { useTopics } from '@/hooks/useTopics';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTranslation } from 'react-i18next';
import { useIsMobile } from '@/hooks/use-mobile';

const DailyPodcast: React.FC = () => {
  const { t } = useTranslation();
  const { language } = useLanguage();
  const { isAuthenticated } = useAuth();
  const isMobile = useIsMobile();
  const [searchParams, setSearchParams] = useSearchParams();

  // Get filters from URL params
  const selectedDateParam = searchParams.get('date') || '';
  const selectedTopic = searchParams.get('topic') || 'all';

  // Convert date string to Date object for DatePicker
  const selectedDate = selectedDateParam ? new Date(selectedDateParam) : undefined;

  // State for audio player
  const [currentlyPlaying, setCurrentlyPlaying] = useState<string | null>(null);
  const [audioElements, setAudioElements] = useState<Record<string, HTMLAudioElement>>({});
  const [currentTime, setCurrentTime] = useState<Record<string, number>>({});
  const [duration, setDuration] = useState<Record<string, number>>({});
  const [playbackRate, setPlaybackRate] = useState<Record<string, number>>({});
  const [volume, setVolume] = useState<Record<string, number>>({});
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});

  // Fetch data
  const { topics } = useTopics();
  const {
    podcastsByDate,
    availableDates,
    loading,
    error,
    refetch,
    getPodcastAudioUrl
  } = usePodcasts({
    date: selectedDateParam || undefined,
    topicId: selectedTopic
  });

  // Update URL params
  const updateFilters = (key: string, value: string) => {
    const newParams = new URLSearchParams(searchParams);
    if (value && value !== 'all' && value !== '') {
      newParams.set(key, value);
    } else {
      newParams.delete(key);
    }
    setSearchParams(newParams);
  };

  // Handle date change from DatePicker
  const handleDateChange = (date: Date | undefined) => {
    const newParams = new URLSearchParams(searchParams);
    if (date) {
      // Format date as YYYY-MM-DD for URL
      const dateString = date.toISOString().split('T')[0];
      newParams.set('date', dateString);
    } else {
      newParams.delete('date');
    }
    setSearchParams(newParams);
  };

  // Audio player functions
  const setupAudioElement = (podcastId: string, audioUrl: string): HTMLAudioElement => {
    let audio = audioElements[podcastId];
    if (!audio) {
      audio = new Audio(audioUrl);

      // Set initial values
      audio.volume = volume[podcastId] || 1;
      audio.playbackRate = playbackRate[podcastId] || 1;

      // Event listeners
      audio.addEventListener('loadstart', () => {
        setIsLoading(prev => ({ ...prev, [podcastId]: true }));
      });

      audio.addEventListener('loadedmetadata', () => {
        setDuration(prev => ({ ...prev, [podcastId]: audio.duration }));
        setIsLoading(prev => ({ ...prev, [podcastId]: false }));
      });

      audio.addEventListener('timeupdate', () => {
        setCurrentTime(prev => ({ ...prev, [podcastId]: audio.currentTime }));
      });

      audio.addEventListener('ended', () => {
        setCurrentlyPlaying(null);
        setCurrentTime(prev => ({ ...prev, [podcastId]: 0 }));
      });

      audio.addEventListener('error', (e) => {
        console.error('Audio playback error:', e);
        setCurrentlyPlaying(null);
        setIsLoading(prev => ({ ...prev, [podcastId]: false }));
      });

      setAudioElements(prev => ({ ...prev, [podcastId]: audio }));
    }
    return audio;
  };

  const togglePlayPause = async (podcastId: string, audioUrl: string) => {
    try {
      if (currentlyPlaying === podcastId) {
        // Pause current audio
        const audio = audioElements[podcastId];
        if (audio) {
          audio.pause();
        }
        setCurrentlyPlaying(null);
      } else {
        // Stop any currently playing audio
        if (currentlyPlaying && audioElements[currentlyPlaying]) {
          audioElements[currentlyPlaying].pause();
        }

        // Setup and play audio
        const audio = setupAudioElement(podcastId, audioUrl);
        await audio.play();
        setCurrentlyPlaying(podcastId);
      }
    } catch (error) {
      console.error('Error playing audio:', error);
      setCurrentlyPlaying(null);
    }
  };

  const seekTo = (podcastId: string, time: number) => {
    const audio = audioElements[podcastId];
    if (audio) {
      audio.currentTime = time;
      setCurrentTime(prev => ({ ...prev, [podcastId]: time }));
    }
  };

  const skipTime = (podcastId: string, seconds: number) => {
    const audio = audioElements[podcastId];
    if (audio) {
      const newTime = Math.max(0, Math.min(audio.duration, audio.currentTime + seconds));
      seekTo(podcastId, newTime);
    }
  };

  const changePlaybackRate = (podcastId: string, rate: number) => {
    const audio = audioElements[podcastId];
    if (audio) {
      audio.playbackRate = rate;
      setPlaybackRate(prev => ({ ...prev, [podcastId]: rate }));
    }
  };

  const changeVolume = (podcastId: string, vol: number) => {
    const audio = audioElements[podcastId];
    if (audio) {
      audio.volume = vol;
      setVolume(prev => ({ ...prev, [podcastId]: vol }));
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'zh' ? 'zh-CN' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Format time for audio player
  const formatTime = (seconds: number): string => {
    if (isNaN(seconds)) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Get language display name
  const getLanguageDisplay = (lang: string) => {
    return lang === 'ZH' ? (language === 'zh' ? '中文' : 'Chinese') : 
           lang === 'EN' ? (language === 'zh' ? '英文' : 'English') : lang;
  };

  // Render podcast card
  const renderPodcastCard = (podcast: PodcastTask) => {
    const audioUrl = getPodcastAudioUrl(podcast);
    const isPlaying = currentlyPlaying === podcast.id;
    const topicName = podcast.topics?.name || podcast.metadata?.topic_name || 'Unknown Topic';
    const podcastCurrentTime = currentTime[podcast.id] || 0;
    const podcastDuration = duration[podcast.id] || 0;
    const podcastPlaybackRate = playbackRate[podcast.id] || 1;
    const podcastVolume = volume[podcast.id] || 1;
    const podcastIsLoading = isLoading[podcast.id] || false;

    return (
      <Card key={podcast.id} className="border border-gray-200 hover:shadow-lg transition-all duration-200 bg-white">
        <CardContent className="p-6">
          {/* Header with topic and language */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs px-2 py-1 bg-green-50 text-green-700 border-green-200">
                {topicName}
              </Badge>
              <Badge variant="secondary" className="text-xs px-2 py-1 bg-blue-50 text-blue-700 border-blue-200">
                {getLanguageDisplay(podcast.language)}
              </Badge>
            </div>
            <div className="text-xs text-muted-foreground">
              {formatDate(podcast.target_date)}
            </div>
          </div>

          {/* Audio player controls */}
          <div className="space-y-4">
            {/* Main controls */}
            <div className="flex items-center gap-3">
              <Button
                variant={isPlaying ? "default" : "outline"}
                size="sm"
                onClick={() => audioUrl && togglePlayPause(podcast.id, audioUrl)}
                disabled={!audioUrl || podcastIsLoading}
                className="flex items-center gap-2"
              >
                {podcastIsLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
                {podcastIsLoading ? 'Loading...' :
                 isPlaying ? t('dailyPodcast.podcastCard.pauseButton') :
                 t('dailyPodcast.podcastCard.playButton')}
              </Button>

              {/* Skip controls */}
              {audioUrl && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => skipTime(podcast.id, -10)}
                    disabled={!isPlaying}
                    className="p-2"
                  >
                    <SkipBack className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => skipTime(podcast.id, 10)}
                    disabled={!isPlaying}
                    className="p-2"
                  >
                    <SkipForward className="h-4 w-4" />
                  </Button>
                </>
              )}

              {/* Settings */}
              {audioUrl && (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="sm" className="p-2">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-80">
                    <div className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium">{t('dailyPodcast.podcastCard.playbackSpeed')}</Label>
                        <div className="flex items-center gap-2 mt-2">
                          {[0.5, 0.75, 1, 1.25, 1.5, 2].map(rate => (
                            <Button
                              key={rate}
                              variant={podcastPlaybackRate === rate ? "default" : "outline"}
                              size="sm"
                              onClick={() => changePlaybackRate(podcast.id, rate)}
                              className="text-xs px-2 py-1"
                            >
                              {rate}x
                            </Button>
                          ))}
                        </div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">{t('dailyPodcast.podcastCard.volume')}</Label>
                        <div className="flex items-center gap-2 mt-2">
                          <Volume2 className="h-4 w-4" />
                          <Slider
                            value={[podcastVolume * 100]}
                            onValueChange={([value]) => changeVolume(podcast.id, value / 100)}
                            max={100}
                            step={1}
                            className="flex-1"
                          />
                          <span className="text-xs text-muted-foreground w-8">
                            {Math.round(podcastVolume * 100)}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              )}

              {!audioUrl && (
                <span className="text-sm text-muted-foreground">
                  {t('dailyPodcast.podcastCard.audioNotAvailable')}
                </span>
              )}
            </div>

            {/* Progress bar */}
            {audioUrl && podcastDuration > 0 && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground min-w-[40px]">
                    {formatTime(podcastCurrentTime)}
                  </span>
                  <Slider
                    value={[podcastCurrentTime]}
                    onValueChange={([value]) => seekTo(podcast.id, value)}
                    max={podcastDuration}
                    step={1}
                    className="flex-1"
                  />
                  <span className="text-xs text-muted-foreground min-w-[40px]">
                    {formatTime(podcastDuration)}
                  </span>
                </div>
                <div className="flex items-center justify-end text-xs text-muted-foreground">
                  {podcastPlaybackRate !== 1 && (
                    <span>{t('dailyPodcast.podcastCard.speed')}: {podcastPlaybackRate}x</span>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  if (!isAuthenticated) {
    return (
      <>
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">{t('dailyPodcast.title')}</h1>
            <p className="text-muted-foreground">Please login to access daily podcasts.</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container-wide mx-auto mobile-content-area mobile-ultra-compact responsive-padding py-8 lg:py-12 space-4xl">
        {/* Header */}
        <div className="flex flex-col gap-6 lg:gap-8 mb-12 lg:mb-16 space-3xl">
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-6 lg:gap-8">
            <div className="flex flex-col gap-4 lg:gap-6 space-lg">
              <h1 className="text-heading-1 text-responsive-2xl font-bold mobile-title text-gradient blur-fade-in">
                {t('dailyPodcast.title')}
              </h1>
              <p className="text-muted-foreground text-lg">
                {t('dailyPodcast.subtitle')}
              </p>
            </div>

            {/* Stats Card */}
            <div className="flex items-center gap-4 px-6 py-4 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl min-w-0 flex-1 sm:flex-none card-3d group hover:bg-gradient-card-hover shadow-card hover:shadow-glow transition-all duration-300">
              <div className="p-3 bg-gradient-primary rounded-xl shadow-card group-hover:shadow-glow group-hover:scale-110 transition-all duration-300 transform-gpu">
                <FileAudio className="h-6 w-6 text-primary-foreground group-hover:animate-pulse" />
              </div>
              <div className="text-center min-w-0">
                <div className="text-2xl font-bold text-primary">
                  {Object.values(podcastsByDate).flat().length}
                </div>
                <div className="text-sm text-muted-foreground font-medium">
                  {t('dailyPodcast.podcasts')}
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <Card className="glass-effect border-border/50 shadow-card hover:shadow-glow transition-all duration-300">
            <CardHeader className="space-md">
              <CardTitle className="flex items-center gap-3 text-lg">
                <div className="p-2 bg-gradient-primary rounded-lg shadow-card">
                  <Filter className="h-5 w-5 text-primary-foreground" />
                </div>
                <span>{t('dailyPodcast.filters.title')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground mb-2 block">
                    {t('dailyPodcast.filters.date')}
                  </Label>
                  <DatePicker
                    value={selectedDate}
                    onValueChange={handleDateChange}
                    placeholder={t('dailyPodcast.filters.selectDate')}
                  />
                </div>

                <div>
                  <Label className="text-sm text-muted-foreground mb-2 block">
                    {t('dailyPodcast.filters.topic')}
                  </Label>
                  <Select value={selectedTopic} onValueChange={(value) => updateFilters('topic', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('dailyPodcast.filters.allTopics')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">{t('dailyPodcast.filters.allTopics')}</SelectItem>
                      {topics.map(topic => (
                        <SelectItem key={topic.id} value={topic.id}>
                          {topic.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Content */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin mr-2" />
            <span>{t('dailyPodcast.loading')}</span>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">{t('dailyPodcast.error')}</h3>
            <Button onClick={refetch} variant="outline">
              {t('dailyPodcast.retry')}
            </Button>
          </div>
        ) : Object.keys(podcastsByDate).length === 0 ? (
          <div className="text-center py-12">
            <FileAudio className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">{t('dailyPodcast.empty.title')}</h3>
            <p className="text-muted-foreground">
              {selectedDateParam ? t('dailyPodcast.empty.noDate') :
               selectedTopic !== 'all' ? t('dailyPodcast.empty.noTopic') :
               t('dailyPodcast.empty.description')}
            </p>
          </div>
        ) : (
          <div className="space-y-8">
            {Object.entries(podcastsByDate)
              .sort(([a], [b]) => b.localeCompare(a)) // Sort dates descending
              .map(([date, podcasts]) => (
                <div key={date}>
                  <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    {formatDate(date)}
                  </h2>
                  <div className="space-y-4">
                    {podcasts.map(renderPodcastCard)}
                  </div>
                </div>
              ))}
          </div>
        )}
      </div>
    </>
  );
};

export default DailyPodcast;
