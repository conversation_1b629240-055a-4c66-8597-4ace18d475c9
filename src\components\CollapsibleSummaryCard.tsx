import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Heart, Clock, ExternalLink, Wand2, Share2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { renderMarkdown } from '@/utils/markdown';

interface CollapsibleSummaryCardProps {
  summary: any;
  platformNames: Record<string, string>;
  getPlatformFromSummaryType: (type: string) => string;
  getTitleFromMetadata: (metadata: any, url: string, index: number) => string;
  isFavorite: (id: string) => boolean;
  toggleFavorite: (id: string) => void;
  isSummaryFavorite: (id: string) => boolean;
  toggleSummaryFavorite: (id: string) => void;
  handleGenerateContent: (summary: any) => void;
  favoritesLoading: boolean;
  summaryFavoritesLoading: boolean;
}

const CollapsibleSummaryCard: React.FC<CollapsibleSummaryCardProps> = ({
  summary,
  platformNames,
  getPlatformFromSummaryType,
  getTitleFromMetadata,
  isFavorite,
  toggleFavorite,
  isSummaryFavorite,
  toggleSummaryFavorite,
  handleGenerateContent,
  favoritesLoading,
  summaryFavoritesLoading,
}) => {
  const { t } = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);

  // 生成预览文本（去除markdown格式，限制长度）
  const getPreviewText = (content: string) => {
    const plainText = content
      .replace(/#{1,6}\s+/g, '') // 移除标题标记
      .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记
      .replace(/\*(.*?)\*/g, '$1') // 移除斜体标记
      .replace(/\[(.*?)\]\(.*?\)/g, '$1') // 移除链接，保留文本
      .replace(/`(.*?)`/g, '$1') // 移除代码标记
      .replace(/[-*]\s+/g, '') // 移除列表标记
      .replace(/\n+/g, ' ') // 将换行符替换为空格
      .trim();
    
    return plainText.length > 150 ? plainText.substring(0, 150) + '...' : plainText;
  };

  // 使用headline作为预览内容，如果没有headline则使用content的前150个字符
  const previewText = summary.headline || getPreviewText(summary.content);

  // 获取摘要标题（从第一行或第一个标题中提取）- 只在展开时使用
  const getSummaryTitle = (content: string) => {
    const lines = content.split('\n');
    // 查找第一个标题行
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('#')) {
        return trimmed.replace(/^#+\s*/, '').trim();
      }
      if (trimmed.length > 0 && !trimmed.startsWith('*') && !trimmed.startsWith('-')) {
        return trimmed.length > 80 ? trimmed.substring(0, 80) + '...' : trimmed;
      }
    }
    return getPreviewText(summary.content).length > 80 ? getPreviewText(summary.content).substring(0, 80) + '...' : getPreviewText(summary.content);
  };

  const summaryTitle = getSummaryTitle(summary.content);

  return (
    <Card
      className="border border-gray-200 hover:shadow-lg transition-all duration-200 cursor-pointer bg-white"
      onClick={() => setIsExpanded(!isExpanded)}
    >
      <CardContent className="p-6">
        {/* 顶部标签行 */}
        <div className="flex items-center gap-2 mb-3">
          <Badge variant="outline" className="text-xs px-2 py-1 bg-green-50 text-green-700 border-green-200">
            {summary.metadata?.topic_name || t('contentSummary.unknownTopic')}
          </Badge>
          <Badge variant="secondary" className="text-xs px-2 py-1 bg-blue-50 text-blue-700 border-blue-200">
            {(() => {
              const platform = summary.metadata?.platform ||
                             summary.posts?.datasources?.platform ||
                             getPlatformFromSummaryType(summary.summary_type);
              return platformNames[platform as keyof typeof platformNames] || platform;
            })()}
          </Badge>
        </div>

        {/* 预览内容 - 使用headline并支持markdown */}
        {!isExpanded && (
          <div className="text-gray-600 text-sm leading-relaxed mb-4">
            {summary.headline ? (
              <div
                className="markdown-content"
                dangerouslySetInnerHTML={{
                  __html: renderMarkdown(summary.headline)
                }}
              />
            ) : (
              <p>{previewText}</p>
            )}
          </div>
        )}

        {/* 展开的完整内容 */}
        {isExpanded && (
          <div className="mb-4">
            <div
              className="text-sm leading-relaxed text-gray-700 whitespace-pre-line markdown-content"
              dangerouslySetInnerHTML={{
                __html: renderMarkdown(summary.content)
              }}
            />

            {/* 相关链接 - 只在展开时显示 */}
            {summary.source_urls && summary.source_urls.length > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-100">
                <div className="text-sm font-medium text-gray-600 mb-2">相关链接:</div>
                <div className="space-y-1">
                  {summary.source_urls.slice(0, 5).map((url: string, index: number) => (
                    <div key={index} className="flex items-center gap-2">
                      <ExternalLink className="h-3 w-3 text-gray-400" />
                      <a
                        href={url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:text-blue-800 underline truncate"
                        title={getTitleFromMetadata(summary.metadata, url, index)}
                        onClick={(e) => e.stopPropagation()}
                      >
                        {getTitleFromMetadata(summary.metadata, url, index)}
                      </a>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 生成内容按钮 - 只在展开时显示 */}
            <div className="mt-4 pt-4 border-t border-gray-100">
              <Button
                onClick={(e) => {
                  e.stopPropagation();
                  handleGenerateContent(summary);
                }}
                variant="outline"
                size="sm"
                className="w-full"
              >
                <Wand2 className="h-4 w-4 mr-2" />
                {t('contentSummary.summary.generateContent')}
              </Button>
            </div>
          </div>
        )}

        {/* 底部信息行 */}
        <div className="flex items-center justify-between">
          {/* 左侧：来源和时间 */}
          <div className="flex items-center gap-4 text-sm text-gray-500">
            <span className="font-medium">
              {summary.metadata?.source_name || summary.posts?.datasources?.source_name || t('contentSummary.unknownSource')}
            </span>
            <span className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {new Date(summary.created_at).toLocaleDateString()}
            </span>
          </div>

          {/* 右侧：操作按钮 */}
          <div className="flex items-center gap-2">
            {/* 收藏数据源按钮 */}
            {(() => {
              const datasourceId = summary.metadata?.datasource_id;
              if (!datasourceId) return null;

              const isFavorited = isFavorite(datasourceId);

              return (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleFavorite(datasourceId);
                  }}
                  disabled={favoritesLoading}
                  className="h-8 w-8 p-0 hover:bg-gray-100"
                  title={isFavorited ? t('contentSummary.favorites.unfavoriteDataSource') : t('contentSummary.favorites.favoriteDataSource')}
                >
                  <Heart
                    className={`h-4 w-4 ${
                      isFavorited
                        ? 'fill-red-500 text-red-500'
                        : 'text-gray-400 hover:text-red-500'
                    }`}
                  />
                </Button>
              );
            })()}

            {/* 收藏摘要按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                toggleSummaryFavorite(summary.id);
              }}
              disabled={summaryFavoritesLoading}
              className="h-8 w-8 p-0 hover:bg-gray-100"
              title={isSummaryFavorite(summary.id) ? t('contentSummary.favorites.unfavoriteSummary') : t('contentSummary.favorites.favoriteSummary')}
            >
              <Heart
                className={`h-4 w-4 ${
                  isSummaryFavorite(summary.id)
                    ? 'fill-blue-500 text-blue-500'
                    : 'text-gray-400 hover:text-blue-500'
                }`}
              />
            </Button>

            {/* 分享按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                // 这里可以添加分享功能
              }}
              className="h-8 w-8 p-0 hover:bg-gray-100"
              title="分享"
            >
              <Share2 className="h-4 w-4 text-gray-400 hover:text-gray-600" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

  );
};

export default CollapsibleSummaryCard;
